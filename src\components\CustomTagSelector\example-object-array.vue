<template>
  <div class="example-container">
    <h2>CustomTagSelector 对象数组格式示例</h2>
    
    <div class="example-section">
      <h3>1. 新的对象数组格式</h3>
      <p>当前值: {{ JSON.stringify(objectArrayValue) }}</p>
      <CustomTagSelector 
        :row-data="rowData"
        v-model="objectArrayValue"
        @change="handleObjectArrayChange"
      />
    </div>

    <div class="example-section">
      <h3>2. 空对象数组（会推断为对象格式）</h3>
      <p>当前值: {{ JSON.stringify(emptyArrayValue) }}</p>
      <CustomTagSelector 
        :row-data="rowData"
        v-model="emptyArrayValue"
        @change="handleEmptyArrayChange"
      />
    </div>

    <div class="example-section">
      <h3>3. 简单数组格式（向后兼容）</h3>
      <p>当前值: {{ JSON.stringify(simpleArrayValue) }}</p>
      <CustomTagSelector 
        :row-data="rowData"
        v-model="simpleArrayValue"
        @change="handleSimpleArrayChange"
      />
    </div>

    <div class="example-section">
      <h3>4. 单个值格式</h3>
      <p>当前值: {{ singleValue }}</p>
      <CustomTagSelector 
        :row-data="rowData"
        v-model="singleValue"
        @change="handleSingleChange"
      />
    </div>

    <div class="log-section">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomTagSelector from './index.vue'

export default {
  name: 'CustomTagSelectorExample',
  components: {
    CustomTagSelector
  },
  data() {
    return {
      rowData: {
        id: '123456',
        md5: 'example-md5-hash'
      },
      // 新的对象数组格式
      objectArrayValue: [
        {
          "id": "1927993177215053824",
          "tagContent": "会也"
        }
      ],
      // 空数组（会推断为对象格式）
      emptyArrayValue: [],
      // 简单数组格式（向后兼容）
      simpleArrayValue: ['1212'],
      // 单个值格式
      singleValue: null,
      // 操作日志
      logs: []
    }
  },
  methods: {
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.unshift(`[${timestamp}] ${message}`)
      if (this.logs.length > 10) {
        this.logs.pop()
      }
    },

    handleObjectArrayChange(value) {
      this.addLog(`对象数组格式变更: ${JSON.stringify(value)}`)
      console.log('对象数组格式变更:', value)
    },

    handleEmptyArrayChange(value) {
      this.addLog(`空数组格式变更: ${JSON.stringify(value)}`)
      console.log('空数组格式变更:', value)
    },

    handleSimpleArrayChange(value) {
      this.addLog(`简单数组格式变更: ${JSON.stringify(value)}`)
      console.log('简单数组格式变更:', value)
    },

    handleSingleChange(value) {
      this.addLog(`单个值格式变更: ${value}`)
      console.log('单个值格式变更:', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.example-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #f9f9f9;

  h3 {
    margin-top: 0;
    color: #333;
  }

  p {
    margin: 10px 0;
    font-family: monospace;
    background-color: #fff;
    padding: 8px;
    border-radius: 3px;
    border: 1px solid #ddd;
  }
}

.log-section {
  margin-top: 40px;
  
  h3 {
    color: #333;
  }
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  margin-bottom: 5px;
  padding: 3px 5px;
  background-color: #fff;
  border-radius: 3px;
}
</style>
