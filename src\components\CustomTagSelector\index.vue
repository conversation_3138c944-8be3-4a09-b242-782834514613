<template>
  <div class="custom-tag-selector">
    <el-select v-model="selectedTagId" size="mini" placeholder="暂无" filterable :loading="loading"
      @change="handleTagChange" style="width: 120px;" class="tag-selector">
      <el-option v-for="tag in tagOptions" :key="tag.id" :label="tag.tagContent" :value="tag.id" />
    </el-select>
  </div>
</template>

<script>
import { listTag } from '@/api/system/tag'
import { updateRiskApi } from '@/api/search/index'

export default {
  name: 'CustomTagSelector',
  props: {
    // 当前行数据，必须包含id和md5字段
    rowData: {
      type: Object,
      required: true,
      validator(value) {
        return value && value.id && value.md5
      }
    },
    // 当前选中的标签ID
    value: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      tagOptions: [], // 标签选项列表
      selectedTagId: this.value // 当前选中的标签ID
    }
  },
  watch: {
    value(newVal) {
      this.selectedTagId = newVal
    }
  },
  created() {
    this.loadTagOptions()
  },
  methods: {
    // 加载标签选项
    async loadTagOptions() {
      try {
        this.loading = true
        const response = await listTag({})

        if (response.data) {
          if (Array.isArray(response.data)) {
            // 非分页响应
            this.tagOptions = response.data
          } else if (response.data.records && Array.isArray(response.data.records)) {
            // 分页响应
            this.tagOptions = response.data.records
          } else {
            this.tagOptions = []
          }
        } else {
          this.tagOptions = []
        }
      } catch (error) {
        console.error('加载标签选项失败:', error)
        this.$message.error('加载标签选项失败')
        this.tagOptions = []
      } finally {
        this.loading = false
      }
    },

    // 处理标签变更
    async handleTagChange(tagId) {
      try {
        const param = {
          md5: this.rowData.md5,
          indexId: this.rowData.id,
          changeType: 2, // 自定义标签变更类型
          changeValue: tagId || null
        }

        const response = await updateRiskApi(param)

        if (response.code === 200) {
          this.$message.success('标签更新成功')
          // 触发v-model更新
          this.$emit('input', tagId)
          // 触发change事件
          this.$emit('change', tagId)
          // 更新本地选中值
          this.selectedTagId = tagId
        } else {
          this.$message.error(response.msg || '标签更新失败')
          // 恢复原值
          this.selectedTagId = this.value
        }
      } catch (error) {
        console.error('更新标签失败:', error)
        this.$message.error('更新标签失败')
        // 恢复原值
        this.selectedTagId = this.value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tag-selector {
  display: inline-block;
  margin: 2px 4px;
}

.tag-selector {
  ::v-deep .el-input.el-input--mini.el-input--suffix {
    // width: 68px;
  }

  ::v-deep .el-input .el-select__caret.is-reverse {
    margin-top: -4px;
  }

  ::v-deep .el-icon-arrow-up:before {
    content: '\e78f';
  }

  ::v-deep .el-input__inner {
    background: #a8dfef;
    color: #006e8f;
  }

  ::v-deep .el-input .el-select__caret {
    color: #006e8f;
  }
}
</style>