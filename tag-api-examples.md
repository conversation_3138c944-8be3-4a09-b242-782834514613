# 标签管理API文档

## 📋 API接口总览

| 方法 | 路径 | 功能 | 参数类型 | 说明 |
|------|------|------|----------|------|
| POST | `/tag/list` | 查询标签列表 | TagQueryVO | 支持多条件查询、分页、排序 |
| POST | `/tag/saveOrUpdate` | 新增或更新标签 | TagVO | 相同内容则更新时间 |
| POST | `/tag/getByIds` | 获取标签详情 | List<Long> | 支持单个和批量查询 |
| POST | `/tag/delete` | 删除标签 | List<Long> | 支持单个和批量删除 |

## 🎯 设计特点

- **统一POST方法**：所有接口都使用POST方法，避免URL长度限制
- **参数对象化**：复杂参数通过请求体传递，便于扩展
- **智能批量处理**：单个和批量操作使用同一接口
- **完整性验证**：严格的参数验证和权限控制
- **用户隔离**：每个用户只能操作自己的标签

## 1. 📝 查询标签列表 - POST /tag/list

### 接口说明
查询标签列表，支持多种查询条件、分页和排序功能。

### 请求参数 (TagQueryVO)

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| tagContent | String | 否 | - | 标签内容，支持模糊查询 |
| userId | Long | 否 | 当前用户ID | 用户ID，不传则查询当前用户 |
| delFlag | String | 否 | "0" | 删除标志：0-存在，2-删除 |
| createTimeStart | Date | 否 | - | 创建时间范围开始 |
| createTimeEnd | Date | 否 | - | 创建时间范围结束 |
| updateTimeStart | Date | 否 | - | 更新时间范围开始 |
| updateTimeEnd | Date | 否 | - | 更新时间范围结束 |
| createBy | String | 否 | - | 创建者，支持模糊查询 |
| orderBy | String | 否 | "updateTime" | 排序字段：createTime/updateTime/tagContent |
| orderDirection | String | 否 | "desc" | 排序方向：asc/desc |
| pageNum | Integer | 否 | - | 页码，传入则启用分页 |
| pageSize | Integer | 否 | - | 每页大小，传入则启用分页 |

### 使用示例

#### 1.1 查询当前用户的所有标签
```json
POST /tag/list
Content-Type: application/json

{}
```

#### 1.2 按标签内容模糊查询
```json
POST /tag/list
Content-Type: application/json

{
    "tagContent": "工作"
}
```

#### 1.3 分页查询
```json
POST /tag/list
Content-Type: application/json

{
    "pageNum": 1,
    "pageSize": 10
}
```

#### 1.4 按时间范围查询
```json
POST /tag/list
Content-Type: application/json

{
    "createTimeStart": "2024-01-01 00:00:00",
    "createTimeEnd": "2024-12-31 23:59:59"
}
```

#### 1.5 复合查询（标签内容 + 分页 + 排序）
```json
POST /tag/list
Content-Type: application/json

{
    "tagContent": "重要",
    "pageNum": 1,
    "pageSize": 5,
    "orderBy": "createTime",
    "orderDirection": "desc"
}
```

#### 1.6 查询已删除的标签
```json
POST /tag/list
Content-Type: application/json

{
    "delFlag": "2"
}
```

#### 1.7 按创建者查询
```json
POST /tag/list
Content-Type: application/json

{
    "createBy": "admin"
}
```

### 响应格式

#### 不分页响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "123456789",
            "userId": "1001",
            "tagContent": "重要工作",
            "createBy": "admin",
            "createTime": "2024-01-15 10:30:00",
            "updateBy": "admin",
            "updateTime": "2024-01-15 10:30:00",
            "delFlag": "0"
        }
    ]
}
```

#### 分页响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "id": "123456789",
                "userId": "1001",
                "tagContent": "重要工作",
                "createBy": "admin",
                "createTime": "2024-01-15 10:30:00",
                "updateBy": "admin",
                "updateTime": "2024-01-15 10:30:00",
                "delFlag": "0"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

## 2. ✏️ 新增或更新标签 - POST /tag/saveOrUpdate

### 接口说明
新增或更新标签。如果当前用户已存在相同内容的标签，则更新该标签的时间；否则创建新标签。

### 请求参数 (TagVO)

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 否 | 标签ID（通常不需要传） |
| userId | Long | 否 | 用户ID（自动设置为当前用户） |
| tagContent | String | 是 | 标签内容，不能为空 |
| delFlag | String | 否 | 删除标志（自动设置） |

### 使用示例

#### 2.1 新增标签
```json
POST /tag/saveOrUpdate
Content-Type: application/json

{
    "tagContent": "重要工作"
}
```

#### 2.2 新增另一个标签
```json
POST /tag/saveOrUpdate
Content-Type: application/json

{
    "tagContent": "学习笔记"
}
```

#### 2.3 重复提交相同内容（会更新时间）
```json
POST /tag/saveOrUpdate
Content-Type: application/json

{
    "tagContent": "重要工作"
}
```

### 响应格式
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": null
}
```

### 业务逻辑
1. 检查当前用户是否已有相同内容的标签
2. 如果不存在，创建新标签
3. 如果已存在，更新该标签的更新时间和更新人
4. 如果内容完全相同且时间未变化，则不做任何操作

## 3. 🔍 获取标签详情 - POST /tag/getByIds

### 接口说明
根据ID列表获取标签详情，支持单个和批量查询。智能返回格式：单个ID返回对象，多个ID返回数组。

### 请求参数
- **类型**: `List<Long>`
- **说明**: 标签ID列表，支持单个或多个ID

### 使用示例

#### 3.1 获取单个标签详情
```json
POST /tag/getByIds
Content-Type: application/json

[123456789]
```

#### 3.2 批量获取标签详情
```json
POST /tag/getByIds
Content-Type: application/json

[123456789, 987654321, 555666777]
```

### 响应格式

#### 单个标签响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": "123456789",
        "userId": "1001",
        "tagContent": "重要工作",
        "createBy": "admin",
        "createTime": "2024-01-15 10:30:00",
        "updateBy": "admin",
        "updateTime": "2024-01-15 10:30:00",
        "delFlag": "0"
    }
}
```

#### 批量标签响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "123456789",
            "userId": "1001",
            "tagContent": "重要工作",
            "createBy": "admin",
            "createTime": "2024-01-15 10:30:00",
            "updateBy": "admin",
            "updateTime": "2024-01-15 10:30:00",
            "delFlag": "0"
        },
        {
            "id": "987654321",
            "userId": "1001",
            "tagContent": "学习笔记",
            "createBy": "admin",
            "createTime": "2024-01-16 14:20:00",
            "updateBy": "admin",
            "updateTime": "2024-01-16 14:20:00",
            "delFlag": "0"
        }
    ]
}
```

### 业务逻辑
1. 验证ID列表不能为空
2. 查询指定ID的标签
3. 检查用户权限（只能查看自己的标签）
4. 验证所有ID都存在
5. 智能返回：单个ID返回对象，多个ID返回数组

## 4. 🗑️ 删除标签 - POST /tag/delete

### 接口说明
删除标签，支持单个和批量删除。采用逻辑删除，数据不会物理删除。

### 请求参数
- **类型**: `List<Long>`
- **说明**: 要删除的标签ID列表

### 使用示例

#### 4.1 单个删除
```json
POST /tag/delete
Content-Type: application/json

[123456789]
```

#### 4.2 批量删除
```json
POST /tag/delete
Content-Type: application/json

[123456789, 987654321, 555666777]
```

### 响应格式
```json
{
    "code": 200,
    "msg": "删除成功",
    "data": null
}
```

### 业务逻辑
1. 验证ID列表不能为空
2. 查询要删除的标签
3. 检查用户权限（只能删除自己的标签）
4. 验证所有ID都存在
5. 检查标签是否已被删除
6. 批量更新删除标志为"2"

### 安全特性
- ✅ **权限控制**: 只能删除当前用户的标签
- ✅ **存在性验证**: 验证所有ID都存在
- ✅ **重复删除检查**: 已删除的标签不能重复删除
- ✅ **逻辑删除**: 数据不会物理删除，可以恢复
- ✅ **详细错误信息**: 明确指出哪些ID不存在或无权限

## 📊 数据模型

### Tag 实体字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | Long | 主键ID（雪花算法生成） | 123456789 |
| userId | Long | 用户ID | 1001 |
| tagContent | String | 标签内容 | "重要工作" |
| createBy | String | 创建者 | "admin" |
| createTime | Date | 创建时间 | "2024-01-15 10:30:00" |
| updateBy | String | 更新者 | "admin" |
| updateTime | Date | 更新时间 | "2024-01-15 10:30:00" |
| delFlag | String | 删除标志（0-存在，2-删除） | "0" |

## ⚠️ 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 500 | 标签内容不能为空 | TagVO.tagContent 为空 |
| 500 | 标签ID列表不能为空 | 传入的ID列表为空 |
| 500 | 标签不存在 | 指定ID的标签不存在 |
| 500 | 部分标签不存在，ID: [xxx] | 批量操作时部分ID不存在 |
| 500 | 无权限查看该标签 | 尝试查看其他用户的标签 |
| 500 | 无权限删除该标签 | 尝试删除其他用户的标签 |
| 500 | 标签已被删除：xxx | 尝试删除已删除的标签 |

## ❓ 常见问题

### Q1: 为什么所有接口都使用POST方法？
**A**:
- 避免URL长度限制，特别是复杂查询条件
- 参数通过请求体传递更安全
- 支持复杂数据结构（如数组、对象）
- 统一接口风格，便于维护

### Q2: 单个操作为什么也要传数组？
**A**:
- 统一接口设计，减少维护成本
- 便于前端封装，一个方法处理单个和批量
- 智能返回格式：单个ID返回对象，多个ID返回数组

### Q3: 删除是物理删除还是逻辑删除？
**A**:
- 采用逻辑删除，设置 `delFlag = "2"`
- 数据不会物理删除，可以恢复
- 查询时默认过滤已删除数据

### Q4: 如何查询已删除的标签？
**A**:
```json
POST /tag/list
{
    "delFlag": "2"
}
```

### Q5: 分页参数如何使用？
**A**:
- 传入 `pageNum` 和 `pageSize` 启用分页
- 不传则返回所有数据
- 分页响应包含 `total`、`pages` 等信息

## 🔧 开发建议

### 前端调用示例 (JavaScript)
```javascript
// 查询标签列表
const queryTags = async (params = {}) => {
    return await fetch('/tag/list', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
    }).then(res => res.json());
};

// 删除标签
const deleteTags = async (ids) => {
    return await fetch('/tag/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(Array.isArray(ids) ? ids : [ids])
    }).then(res => res.json());
};
```

### 后端扩展建议
1. **添加新查询条件**: 在 `TagQueryVO` 中添加字段
2. **添加新排序字段**: 在 `TagService.queryTags()` 中扩展 switch 语句
3. **添加新业务逻辑**: 在对应的 Service 方法中实现
