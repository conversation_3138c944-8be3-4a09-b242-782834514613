# CustomTagSelector 兼容性测试

## 测试场景

### 1. 单个值格式测试

```javascript
// 测试数据
const testData = {
  rowData: { id: '123', md5: 'abc123' },
  value: null
}

// 预期行为
// 1. 初始显示: 空（placeholder: "暂无"）
// 2. 选择标签456后: value变为456
// 3. 清空选择后: value变为null
```

### 2. 数组格式测试

```javascript
// 测试数据
const testData = {
  rowData: { id: '123', md5: 'abc123' },
  value: []
}

// 预期行为
// 1. 初始显示: 空（placeholder: "暂无"）
// 2. 选择标签456后: value变为[456]
// 3. 清空选择后: value变为[]
```

### 3. 已有数据的简单数组格式测试

```javascript
// 测试数据
const testData = {
  rowData: { id: '123', md5: 'abc123' },
  value: [789]
}

// 预期行为
// 1. 初始显示: 标签789的名称
// 2. 选择标签456后: value变为[456]
// 3. 清空选择后: value变为[]
```

### 4. 对象数组格式测试（新格式）

```javascript
// 测试数据
const testData = {
  rowData: { id: '123', md5: 'abc123' },
  value: [
    {
      "id": "1927993177215053824",
      "tagContent": "会也"
    }
  ]
}

// 预期行为
// 1. 初始显示: "会也"
// 2. 选择标签456后: value变为[{"id": "456", "tagContent": "新标签名"}]
// 3. 清空选择后: value变为[]
```

### 5. 空对象数组格式测试

```javascript
// 测试数据
const testData = {
  rowData: { id: '123', md5: 'abc123' },
  value: []
}

// 预期行为（默认推断为对象数组格式）
// 1. 初始显示: 空（placeholder: "暂无"）
// 2. 选择标签456后: value变为[{"id": "456", "tagContent": "标签名"}]
// 3. 清空选择后: value变为[]
```

## 测试用例

### 用例1: 格式保持一致性

```vue
<template>
  <div>
    <!-- 单个值格式 -->
    <CustomTagSelector
      :row-data="rowData"
      v-model="singleValue"
      @change="handleSingleChange"
    />
    <p>单个值: {{ singleValue }}</p>

    <!-- 数组格式 -->
    <CustomTagSelector
      :row-data="rowData"
      v-model="arrayValue"
      @change="handleArrayChange"
    />
    <p>数组值: {{ arrayValue }}</p>
  </div>
</template>

<script>
export default {
  data() {
    return {
      rowData: { id: '123', md5: 'abc123' },
      singleValue: null,
      arrayValue: []
    }
  },
  methods: {
    handleSingleChange(value) {
      console.log('单个值变更:', value, typeof value)
      // 应该输出: 数字或null
    },
    handleArrayChange(value) {
      console.log('数组值变更:', value, Array.isArray(value))
      // 应该输出: 数组，Array.isArray(value) === true
    }
  }
}
</script>
```

### 用例2: contentMeta对象数组格式兼容（新格式）

```vue
<template>
  <el-table :data="tableData">
    <el-table-column label="自定义标签">
      <template slot-scope="scope">
        <CustomTagSelector
          :row-data="scope.row"
          v-model="scope.row.contentMeta"
          @change="handleTagChange(scope.row, $event)"
        />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        {
          id: '1',
          md5: 'hash1',
          contentMeta: [
            {
              "id": "1927993177215053824",
              "tagContent": "会也"
            }
          ] // 新的对象数组格式
        },
        {
          id: '2',
          md5: 'hash2',
          contentMeta: [] // 空数组（会推断为对象数组格式）
        },
        {
          id: '3',
          md5: 'hash3',
          contentMeta: ['1212'] // 旧的简单数组格式（向后兼容）
        }
      ]
    }
  },
  methods: {
    handleTagChange(row, value) {
      console.log('行数据:', row.id, '标签变更:', value)
      // value应该保持原始格式：
      // 对象数组 → 对象数组
      // 简单数组 → 简单数组
    }
  }
}
</script>
```

## 验证要点

1. **输入格式检测**: 组件能正确识别输入是单个值、简单数组还是对象数组
2. **显示逻辑**:
   - 简单数组格式时正确显示第一个元素对应的标签名
   - 对象数组格式时正确显示第一个对象的tagContent
3. **输出格式**: 输出格式与输入格式保持一致
4. **对象数组处理**: 能正确构造包含id和tagContent的对象
5. **API调用**: 无论输入格式如何，API都接收正确的单个标签ID
6. **错误恢复**: 失败时能正确恢复到原始格式
7. **空数组推断**: 空数组能正确推断为对象数组格式

## 预期结果

- ✅ 单个值输入 → 单个值输出
- ✅ 简单数组输入 → 简单数组输出
- ✅ 对象数组输入 → 对象数组输出
- ✅ 空值处理正确
- ✅ API调用参数正确（始终是单个ID）
- ✅ 错误恢复机制正常
- ✅ 对象数组格式构造正确
