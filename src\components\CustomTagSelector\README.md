# CustomTagSelector 自定义标签选择器组件

## 📋 功能说明

自定义标签选择器组件，用于在舆情数据列表中为每条数据添加自定义标签。支持多选标签、搜索过滤、实时更新等功能。

## 🎯 特性

- ✅ **多选标签**: 支持为单条数据选择多个自定义标签
- ✅ **搜索过滤**: 支持按标签名称搜索过滤
- ✅ **实时更新**: 标签变更后立即调用API更新
- ✅ **友好界面**: 直观的标签显示和选择界面
- ✅ **错误处理**: 完善的错误提示和异常处理
- ✅ **可复用**: 组件化设计，方便在其他页面使用

## 📦 使用方法

### 1. 基本用法

```vue
<template>
  <CustomTagSelector 
    :row-data="rowData"
    v-model="selectedTagIds"
    @change="handleTagChange"
  />
</template>

<script>
import CustomTagSelector from '@/components/CustomTagSelector/index.vue'

export default {
  components: {
    CustomTagSelector
  },
  data() {
    return {
      rowData: {
        id: '123456',
        md5: 'abc123',
        title: '示例标题'
      },
      selectedTagIds: [1, 2, 3] // 已选标签ID数组
    }
  },
  methods: {
    handleTagChange(tagIds) {
      console.log('标签已更新:', tagIds)
    }
  }
}
</script>
```

### 2. 在表格中使用

```vue
<el-table :data="tableData">
  <el-table-column label="自定义标签">
    <template slot-scope="scope">
      <CustomTagSelector 
        :row-data="scope.row"
        v-model="scope.row.customTags"
        @change="handleCustomTagChange(scope.row, $event)"
      />
    </template>
  </el-table-column>
</el-table>
```

## 🔧 Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| rowData | Object | 是 | - | 当前行数据，必须包含id和md5字段 |
| value | Array | 否 | [] | 当前已选标签ID数组 |

## 📤 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | tagIds: Array | v-model双向绑定事件 |
| change | tagIds: Array | 标签变更事件 |

## 🔌 API接口

组件依赖以下API接口：

### 1. 标签列表接口
- **接口**: `listTag(data)`
- **文件**: `@/api/system/tag`
- **说明**: 获取所有可用标签列表

### 2. 标签更新接口
- **接口**: `updateRiskApi(param)`
- **文件**: `@/api/search/index`
- **参数**:
  ```javascript
  {
    md5: row.md5,        // 数据MD5
    indexId: row.id,     // 数据ID
    changeType: 2,       // 变更类型：2-自定义标签
    changeValue: tagIds  // 标签ID数组
  }
  ```

## 🎨 样式说明

组件使用scoped样式，主要样式类：

- `.custom-tag-selector`: 组件容器
- `.selected-tags`: 已选标签显示区域
- `.tag-selector-content`: 弹窗内容区域
- `.tag-list`: 标签列表区域
- `.tag-grid`: 标签网格布局
- `.tag-checkbox`: 标签复选框样式

## 📝 注意事项

1. **数据格式**: rowData必须包含id和md5字段
2. **权限控制**: 确保用户有标签管理权限
3. **网络异常**: 组件已处理网络请求异常情况
4. **性能优化**: 标签列表会缓存，避免重复请求

## 🔄 更新日志

- **v1.0.0**: 初始版本，支持基本的标签选择和更新功能
